#!/usr/bin/env python3
"""
广韵数据导入脚本的包装器
确保正确的模块导入路径
"""
import sys
import os
from pathlib import Path

# 确保我们在正确的目录中
script_dir = Path(__file__).parent
os.chdir(script_dir)

# 添加当前目录到Python路径
sys.path.insert(0, str(script_dir))

# 现在导入并运行原始脚本
if __name__ == "__main__":
    # 导入原始脚本的主要功能
    import importlib.util
    
    spec = importlib.util.spec_from_file_location(
        "import_guangyun", 
        script_dir / "scripts" / "import" / "import_guangyun.py"
    )
    import_guangyun = importlib.util.module_from_spec(spec)
    
    # 设置正确的路径
    sys.path.insert(0, str(script_dir))
    
    # 执行脚本
    spec.loader.exec_module(import_guangyun)
