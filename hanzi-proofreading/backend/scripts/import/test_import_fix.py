#!/usr/bin/env python3
import sys
import os
import logging
from pathlib import Path

print(f"Current working directory: {os.getcwd()}")
print(f"Script file: {__file__}")
print(f"Script path: {Path(__file__)}")

# 添加backend目录到路径，以便导入app模块
backend_path = str(Path(__file__).parent.parent.parent)
print(f"Adding to sys.path: {backend_path}")
sys.path.insert(0, backend_path)

print("sys.path entries:")
for i, path in enumerate(sys.path[:5]):  # 只显示前5个
    print(f"  {i}: {path}")

print(f"\nTrying to import app...")
try:
    from app.database import SessionLocal, DATABASE_URL
    from app import models, schemas, crud
    print("✓ Successfully imported all app modules")
except ImportError as e:
    print(f"✗ Failed to import: {e}")
    import traceback
    traceback.print_exc()
