#!/usr/bin/env python3
import sys
from pathlib import Path

# 模拟 import_guangyun.py 的路径设置
script_path = Path(__file__).parent / "scripts" / "import" / "import_guangyun.py"
backend_path = script_path.parent.parent.parent
print(f"Adding to sys.path: {backend_path}")
sys.path.insert(0, str(backend_path))

print("sys.path entries:")
for i, path in enumerate(sys.path):
    print(f"  {i}: {path}")

print(f"\nTrying to import app...")
try:
    import app
    print("✓ Successfully imported app")
    print(f"app module: {app}")
    print(f"app.__file__: {app.__file__}")
except ImportError as e:
    print(f"✗ Failed to import app: {e}")

print(f"\nTrying to import app.database...")
try:
    from app.database import SessionLocal, DATABASE_URL
    print("✓ Successfully imported app.database")
except ImportError as e:
    print(f"✗ Failed to import app.database: {e}")
