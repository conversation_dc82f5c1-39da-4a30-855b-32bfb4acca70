#!/usr/bin/env python3
import sys
from pathlib import Path

# 测试路径设置
script_path = Path(__file__)
print(f"Script path: {script_path}")
print(f"Script absolute path: {script_path.resolve()}")

# 模拟 import_guangyun.py 的路径
import_script_path = Path(__file__).parent / "scripts" / "import" / "import_guangyun.py"
print(f"Import script path: {import_script_path}")
print(f"Import script exists: {import_script_path.exists()}")

# 测试不同的父目录级别
print(f"Parent: {import_script_path.parent}")
print(f"Parent.parent: {import_script_path.parent.parent}")
print(f"Parent.parent.parent: {import_script_path.parent.parent.parent}")

# 检查 app 目录
app_path_1 = import_script_path.parent.parent / "app"
app_path_2 = import_script_path.parent.parent.parent / "app"

print(f"App path (parent.parent/app): {app_path_1}")
print(f"App path exists: {app_path_1.exists()}")

print(f"App path (parent.parent.parent/app): {app_path_2}")
print(f"App path exists: {app_path_2.exists()}")

# 检查当前目录的 app
current_app = Path(__file__).parent / "app"
print(f"Current app path: {current_app}")
print(f"Current app exists: {current_app.exists()}")
